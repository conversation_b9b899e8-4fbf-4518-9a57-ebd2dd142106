# 🖼️ Funcionalidade de Imagem de Capa

## 📋 Visão Geral

A nova seção de **Imagem de Capa** permite aos usuários personalizar a imagem do logo/perfil que aparece no topo da página. Esta funcionalidade foi implementada seguindo os padrões de design e acessibilidade estabelecidos no projeto.

## 🎯 Localização

A seção está localizada na aba **"Config"** do modal de configurações, como a primeira opção na lista de configurações avançadas.

## ✨ Características

### 🎨 Design e Layout
- **Layout de duas colunas**: Ícone à esquerda, conteúdo à direita
- **Preview circular**: Mostra a imagem atual em formato circular (120px desktop, 100px mobile/tablet)
- **Overlay de upload**: Aparece ao passar o mouse com ícone e texto "Clique para alterar"
- **Botões de ação**: "Selecionar Imagem" e "Remover" (quando aplicável)
- **Informações do arquivo**: Mostra nome e tamanho do arquivo selecionado

### 🔧 Funcionalidades

#### Upload de Imagem
- **Clique para selecionar**: Clique na área de preview ou no botão "Selecionar Imagem"
- **Drag & Drop**: Arraste e solte arquivos diretamente na área de preview
- **Navegação por teclado**: Suporte para Enter e Espaço na área de preview

#### Validações
- **Formatos suportados**: JPG, JPEG, PNG, WebP, GIF
- **Tamanho máximo**: 5MB
- **Feedback de erro**: Mensagens toast para arquivos inválidos

#### Estados Visuais
- **Loading**: Indicador de carregamento durante o processamento
- **Drag over**: Destaque visual durante drag & drop
- **Preview atualizado**: Mostra a nova imagem imediatamente

### 📱 Responsividade

#### Desktop (>1024px)
- Preview: 120px × 120px
- Botões lado a lado
- Gap de 16px entre elementos

#### Tablet (768px - 1023px)
- Preview: 100px × 100px
- Botões com gap reduzido (10px)
- Padding ajustado

#### Mobile (<768px)
- Preview: 100px × 100px
- Botões empilhados verticalmente
- Largura total para botões
- Altura mínima de 44px para touch

### 🎭 Animações e Transições

- **Hover effects**: Scale 1.05 e sombra dourada no preview
- **Transições suaves**: 300ms cubic-bezier para todos os estados
- **Feedback de toque**: Scale 0.98 em dispositivos móveis
- **Fade da imagem**: Transição suave ao trocar imagens

### ♿ Acessibilidade

- **Labels apropriados**: `aria-label` no input de arquivo
- **Alt text dinâmico**: Atualizado conforme o estado da imagem
- **Navegação por teclado**: Suporte completo para Enter e Espaço
- **Contraste adequado**: Cores seguem padrões WCAG
- **Touch targets**: Mínimo 44px em dispositivos móveis

## 🔄 Fluxo de Funcionamento

### 1. Seleção de Imagem
```
Usuário clica → Input de arquivo abre → Arquivo selecionado → Validação
```

### 2. Processamento
```
Validação OK → FileReader → Base64 → Preview atualizado → LocalStorage
```

### 3. Aplicação
```
Preview atualizado → Logo principal atualizado → Feedback de sucesso
```

### 4. Persistência
```
Dados salvos no localStorage → Carregamento automático na próxima visita
```

## 💾 Armazenamento

- **Formato**: Base64 string no localStorage
- **Chave**: `currentConfig.settings.coverImage`
- **Backup automático**: Integrado ao sistema existente
- **Restauração**: Carregamento automático na inicialização

## 🎨 Classes CSS (BEM)

### Estrutura Principal
- `.cover-image-section` - Container principal
- `.cover-image-preview` - Área de preview
- `.preview-image` - Elemento img
- `.upload-overlay` - Overlay de upload

### Estados
- `.cover-image-preview.loading` - Estado de carregamento
- `.cover-image-preview.drag-over` - Estado de drag over

### Controles
- `.image-actions` - Container dos botões
- `.btn-image-action` - Botões de ação
- `.btn-image-action.btn-remove` - Botão de remover
- `.image-info` - Informações do arquivo
- `.image-details` - Detalhes do arquivo

## 🔧 Métodos JavaScript

### Principais
- `bindCoverImageEvents()` - Vincula todos os eventos
- `handleImageUpload(event)` - Processa upload via input
- `processImageFile(file)` - Valida e processa arquivo
- `updateImagePreview(imageSrc, file)` - Atualiza preview
- `removeImage()` - Remove imagem personalizada
- `updateMainLogo(imageSrc)` - Atualiza logo principal
- `loadSavedCoverImage()` - Carrega imagem salva

### Utilitários
- `formatFileSize(bytes)` - Formata tamanho do arquivo
- Validações de tipo e tamanho
- Gerenciamento de estados visuais

## 🚀 Integração

A funcionalidade está totalmente integrada ao sistema existente:

- **Sistema de configurações**: Usa a mesma estrutura de localStorage
- **Sistema de toast**: Feedback usando o sistema existente
- **Padrões de design**: Segue BEM e estrutura de duas colunas
- **Responsividade**: Integrado ao sistema de breakpoints
- **Acessibilidade**: Segue padrões WCAG estabelecidos

## 🔄 Compatibilidade

- **Navegadores**: Chrome, Firefox, Safari, Edge (versões recentes)
- **Dispositivos**: Desktop, tablet, mobile
- **APIs**: FileReader, localStorage, drag & drop
- **Formatos**: JPG, PNG, WebP, GIF
- **Tamanho**: Até 5MB por arquivo
