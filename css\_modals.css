/* Modal de Configurações com Magic UI */
.config-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 2000;
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    padding: 20px;
}

.config-modal.active {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    animation: magicModalFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Magic Card effect para o modal */
.config-modal-content {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(212, 175, 55, 0.1);
    animation: magicModalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 20px;
    position: relative;
    border: 1px solid transparent;
    background-clip: padding-box;
    /* Prevenir mudanças bruscas de altura */
    contain: layout style paint;
    /* Estabilizar dimensões */
    box-sizing: border-box;
    /* Estrutura flexível para controle de altura */
    display: flex;
    flex-direction: column;
}

/* Neon gradient border effect */
.config-modal-content::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg,
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: neonBorderRotate 3s linear infinite;
}

/* Spotlight effect que segue o mouse */
.config-modal-content::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: radial-gradient(
        600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(212, 175, 55, 0.1),
        transparent 40%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.config-modal-content:hover::after {
    opacity: 1;
}

/* Header do Modal com Magic UI */
.config-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
    position: relative;
    overflow: hidden;
}

/* Animated gradient text para o título */
.config-header h2 {
    background: linear-gradient(
        45deg,
        var(--accent-color),
        #f1c40f,
        var(--accent-color),
        #ffd700
    );
    background-size: 300% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Shimmer effect para o ícone do título */
.config-header h2 i {
    background: var(--accent-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s ease-in-out infinite;
}

/* Corpo do Modal */
.config-body {
    padding: 30px;
    flex: 1; /* CORREÇÃO: Usar flex para ocupar espaço disponível */
    overflow: visible; /* FIX: Permite que o dropdown apareça */
    /* Estabilizar scroll */
    scroll-behavior: smooth;
    contain: layout style;
    /* Altura mínima para evitar colapso */
    min-height: 400px;
    /* Estrutura flexível */
    display: flex;
    flex-direction: column;
}

.config-section {
    margin-bottom: 40px;
    /* Estabilizar seções */
    contain: layout style;
}

.config-section h3 {
    color: var(--accent-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--secondary-color);
}

/* Lista de Links com Magic UI */
.links-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    /* Altura mínima para evitar colapso */
    min-height: 200px;
    contain: layout style;
}

/* Magic Cards para os itens de links - Mobile First */
.link-item {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #1f1f1f 100%);
    border-radius: var(--mobile-border-radius);
    /* Mobile: Padding otimizado para touch */
    padding: var(--mobile-padding);
    min-height: var(--touch-target-min);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    /* Touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Mobile: Efeitos desabilitados para performance */
.link-item::before {
    display: none;
}

.link-item::after {
    display: none;
}

/* Mobile: Hover simplificado */
.link-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* Mobile First: Link Item Header - Layout como na imagem */
.link-item-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 0;
}

/* Ícone: Posicionado na segunda linha com destaque visual */
.link-preview {
    width: var(--touch-target-min);
    height: var(--touch-target-min);
    border-radius: 12px; /* Bordas mais arredondadas */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem; /* Ícone ligeiramente maior */
    color: white;
    flex-shrink: 0;
    /* Destaque visual com sombra sutil */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    /* Background overlay para melhor contraste */
    position: relative;
    /* Transição suave para efeitos hover */
    transition: all var(--transition);
}

/* Overlay para melhorar contraste do ícone */
.link-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    pointer-events: none;
    transition: var(--transition);
}

/* Efeito hover sutil para o ícone */
.link-item:hover .link-preview {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.link-item:hover .link-preview::before {
    background: rgba(0, 0, 0, 0.05);
}

/* Container do conteúdo (nome + controles) na direita */
.link-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
    gap: 12px;
}

.link-details {
    flex: 1;
    min-width: 0;
}

.link-details h4 {
    color: var(--text-light);
    font-size: var(--mobile-font-size-large);
    font-weight: 600;
    margin-bottom: 0;
    line-height: 1.3;
    /* Mobile: Permitir quebra natural do texto, sem truncation */
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    word-wrap: break-word;
}

/* URL removida completamente do layout */
.link-details small {
    display: none;
}

/* Controles: No lugar do texto "Clique para acessar" */
.link-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    min-height: var(--touch-target-min);
}

/* Mobile: Melhor identificação visual dos botões */
.link-controls .control-btn {
    position: relative;
}

/* Toggle Switch - Mobile Optimized */
.toggle-switch {
    position: relative;
    /* Mobile: Tamanho otimizado para touch */
    width: 52px;
    height: 28px;
    background: #333;
    border-radius: 28px;
    cursor: pointer;
    transition: var(--transition);
    /* Touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.toggle-switch.active {
    background: var(--accent-color);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    /* Mobile: Tamanho ajustado */
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
}

.toggle-switch.active::after {
    /* Mobile: Distância ajustada */
    transform: translateX(24px);
}

/* ===== ABA DE CONFIGURAÇÕES ===== */

.settings-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* ===== SEÇÃO DE IMAGEM DE CAPA ===== */

.cover-image-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
}

.cover-image-preview {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--secondary-color);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 auto;
    background: var(--secondary-color);
}

.cover-image-preview:hover {
    border-color: var(--accent-color);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    color: var(--text-light);
    font-size: 0.8rem;
    text-align: center;
    gap: 8px;
}

.cover-image-preview:hover .upload-overlay {
    opacity: 1;
}

.upload-overlay .icon-svg {
    width: 24px;
    height: 24px;
    color: var(--accent-color);
}

.file-input {
    display: none;
}

.image-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-image-action {
    background: var(--secondary-color);
    border: 1px solid transparent;
    border-radius: 8px;
    color: var(--text-light);
    padding: 10px 16px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 44px;
    font-family: inherit;
}

.btn-image-action:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    border-color: var(--accent-color);
    transform: translateY(-1px);
}

.btn-image-action:active {
    transform: translateY(0);
}

.btn-image-action.btn-edit {
    background: rgba(108, 92, 231, 0.1);
    border-color: rgba(108, 92, 231, 0.3);
    color: #6c5ce7;
}

.btn-image-action.btn-edit:hover {
    background: #6c5ce7;
    color: white;
    border-color: #6c5ce7;
}

.btn-image-action.btn-remove {
    background: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

.btn-image-action.btn-remove:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.btn-image-action .icon-svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.image-info {
    text-align: center;
    padding: 8px 12px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.image-details {
    color: var(--text-gray);
    font-size: 0.75rem;
    line-height: 1.4;
}

/* Estados de loading */
.cover-image-preview.loading {
    pointer-events: none;
}

.cover-image-preview.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 2px solid transparent;
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estado de drag over */
.cover-image-preview.drag-over {
    border-color: var(--accent-color);
    background: rgba(212, 175, 55, 0.1);
    transform: scale(1.05);
}

.cover-image-preview.drag-over .upload-overlay {
    opacity: 1;
    background: rgba(212, 175, 55, 0.2);
}

/* ===== MODAL DE EDIÇÃO DE IMAGEM ===== */

.image-edit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 3000; /* Acima do modal de configurações */
    overflow: hidden;
    padding: 20px;
}

.image-edit-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: magicModalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-edit-modal-content {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(212, 175, 55, 0.1);
    animation: magicModalSlideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid transparent;
    background-clip: padding-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Magic UI effects para o modal de edição */
.image-edit-modal-content::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg,
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: neonBorderRotate 3s linear infinite;
}

.image-edit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
    flex-shrink: 0;
}

.image-edit-header h3 {
    color: var(--accent-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.image-edit-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
}

.image-edit-close:hover {
    background: rgba(212, 175, 55, 0.1);
    color: var(--accent-color);
}

.image-edit-body {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.image-edit-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
    height: 100%;
}

.image-editor-container {
    display: flex;
    gap: 25px;
    align-items: flex-start;
    flex: 1;
}

.image-canvas-wrapper {
    position: relative;
    flex: 1;
    min-height: 300px;
    background: var(--secondary-color);
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid rgba(212, 175, 55, 0.2);
}

.image-canvas {
    width: 100%;
    height: 100%;
    cursor: grab;
    display: block;
}

.image-canvas:active {
    cursor: grabbing;
}

.crop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.crop-circle {
    width: 200px;
    height: 200px;
    border: 3px solid var(--accent-color);
    border-radius: 50%;
    box-shadow:
        0 0 0 9999px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(212, 175, 55, 0.5);
    position: relative;
}

.crop-circle::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.image-preview-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
}

.preview-label {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 600;
}

.preview-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--accent-color);
    background: var(--secondary-color);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.preview-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.image-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    background: var(--secondary-color);
    padding: 15px 20px;
    border-radius: 12px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.btn-zoom {
    background: rgba(212, 175, 55, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    color: var(--accent-color);
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
}

.btn-zoom:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: scale(1.05);
}

.btn-zoom:active {
    transform: scale(0.95);
}

.btn-zoom .icon-svg {
    width: 20px;
    height: 20px;
}

.zoom-slider-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 200px;
}

.zoom-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.zoom-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    transition: all 0.3s ease;
}

.zoom-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.5);
}

.zoom-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    transition: all 0.3s ease;
}

.zoom-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.5);
}

.zoom-value {
    color: var(--text-light);
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
}

.position-hint {
    text-align: center;
}

.position-hint small {
    color: var(--text-gray);
    font-size: 0.8rem;
}

.image-edit-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
    gap: 15px;
    flex-shrink: 0;
}

.image-edit-footer .btn-secondary,
.image-edit-footer .btn-primary {
    min-height: 44px;
    padding: 12px 20px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
}

.image-edit-footer .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
}

.image-edit-footer .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.image-edit-footer .btn-primary {
    background: var(--accent-color);
    border: 1px solid var(--accent-color);
    color: var(--primary-color);
}

.image-edit-footer .btn-primary:hover {
    background: #e6c76b;
    border-color: #e6c76b;
    transform: translateY(-1px);
}

.image-edit-footer .btn-secondary .icon-svg,
.image-edit-footer .btn-primary .icon-svg {
    width: 16px;
    height: 16px;
}

.setting-item {
    background: var(--secondary-color);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.setting-item:hover {
    border-color: rgba(212, 175, 55, 0.3);
    transform: translateY(-1px);
}

.setting-info h4 {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.setting-info small {
    color: var(--text-gray);
    font-size: 0.85rem;
    line-height: 1.4;
}

.setting-control {
    flex-shrink: 0;
}

/* ===== MODAL DE EDIÇÃO ===== */

/* Modal de Edição */
.edit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 9999; /* Garante que o modal de edição apareça acima de tudo */
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    padding: 20px;
}

.edit-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

/* Edit Modal com Magic UI */
.edit-modal-content {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 500px;
    max-height: calc(100vh - 40px); /* ✅ CORREÇÃO: Altura máxima para evitar overflow */
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(212, 175, 55, 0.1);
    animation: magicModalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid transparent;
    background-clip: padding-box;
    display: flex; /* ✅ CORREÇÃO: Estrutura flexível */
    flex-direction: column; /* ✅ CORREÇÃO: Layout vertical */
    overflow: hidden; /* ✅ CORREÇÃO: Sem scroll no container principal */
}

/* Neon gradient border effect para edit modal */
.edit-modal-content::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg,
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: neonBorderRotate 3s linear infinite;
}

/* Spotlight effect para edit modal */
.edit-modal-content::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: radial-gradient(
        400px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(212, 175, 55, 0.1),
        transparent 40%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.edit-modal-content:hover::after {
    opacity: 1;
}

/* Header do Modal de Edição com Magic UI */
.edit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
    position: relative;
    overflow: hidden;
}

/* Animated gradient text para o título do edit modal */
.edit-header h3 {
    background: linear-gradient(
        45deg,
        var(--accent-color),
        #f1c40f,
        var(--accent-color),
        #ffd700
    );
    background-size: 300% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

/* Shimmer effect para o ícone do título do edit modal */
.edit-header h3 i {
    background: var(--accent-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s ease-in-out infinite;
}

/* Corpo do Modal de Edição */
.edit-body {
    padding: 25px;
    flex: 1; /* ✅ CORREÇÃO: Ocupar espaço disponível */
    overflow-y: auto; /* ✅ CORREÇÃO: Scroll apenas no corpo quando necessário */
    overflow-x: hidden; /* ✅ CORREÇÃO: Sem scroll horizontal */
}
