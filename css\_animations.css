/* Animaç<PERSON><PERSON> */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes aurora {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes border-beam {
    0% { offset-distance: 0%; }
    100% { offset-distance: 100%; }
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(var(--grid-size), var(--grid-size)); }
}

@keyframes ripple {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(4); opacity: 0; }
}

@keyframes text-blur-in {
    0% { opacity: 0; filter: blur(10px); transform: translateY(20px); }
    100% { opacity: 1; filter: blur(0px); transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== CLASSES DE TRANSFORMAÇÃO - MIGRADAS DE ESTILOS INLINE ===== */

/* Transformações de transição de campos */
.field-transition-out {
    transform: translateY(-10px);
    opacity: 0.3;
}

.field-transition-in-start {
    transform: translateY(10px);
    opacity: 0.3;
}

.field-transition-in-end {
    transform: translateY(0);
    opacity: 1;
}

/* Transformações de modal mobile */
.modal-swipe-transform {
    transition: none;
}

.modal-swipe-reset {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    opacity: 1;
}

/* Feedback tátil para touch */
.touch-feedback {
    transform: scale(0.98);
}

.touch-feedback-reset {
    transform: scale(1);
}
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes mobileSlideUp {
    0% {
        transform: translateY(100%);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes tabFadeIn {
    0% {
        opacity: 0;
        /* Remover transform para evitar reflows */
    }
    100% {
        opacity: 1;
    }
}

@keyframes magicModalFadeIn {
    0% {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    100% {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
}

@keyframes magicModalSlideUp {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0px);
    }
}

@keyframes neonBorderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes mobileModalFadeIn {
    0% {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    100% {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
}

@keyframes mobileModalFadeOut {
    0% {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
    100% {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
}

@keyframes mobileSlideDown {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(100%);
        opacity: 0.8;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
