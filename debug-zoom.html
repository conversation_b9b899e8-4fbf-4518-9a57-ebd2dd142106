<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - <PERSON><PERSON><PERSON><PERSON> de Zoom</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Debug - Botões de Zoom do Modal</h1>
        
        <div class="instructions">
            <h3>📋 Instruções para Debug:</h3>
            <ol>
                <li><strong>Abra a aplicação principal</strong> em uma nova aba: <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
                <li><strong>Carregue uma imagem</strong> para abrir o modal de edição</li>
                <li><strong>Volte para esta aba</strong> e execute os testes abaixo</li>
                <li><strong>Verifique o console</strong> (F12) para logs detalhados</li>
                <li><strong>Se necessário</strong>, use as funções de teste diretas no console</li>
            </ol>
        </div>

        <div class="debug-section">
            <h3>1. Verificação de Elementos</h3>
            <p>Verifica se os elementos do modal estão presentes no DOM.</p>
            <button class="test-button" onclick="checkElements()">Verificar Elementos</button>
            <div id="elements-status" class="status" style="display: none;"></div>
        </div>

        <div class="debug-section">
            <h3>2. Teste de Cliques Diretos</h3>
            <p>Simula cliques diretos nos botões de zoom.</p>
            <button class="test-button" onclick="testDirectClicks()">Testar Cliques Diretos</button>
            <div id="clicks-status" class="status" style="display: none;"></div>
        </div>

        <div class="debug-section">
            <h3>3. Teste de Funções de Zoom</h3>
            <p>Testa as funções de zoom diretamente (bypassa os botões).</p>
            <button class="test-button" onclick="testZoomFunctions()">Testar Funções de Zoom</button>
            <div id="functions-status" class="status" style="display: none;"></div>
        </div>

        <div class="debug-section">
            <h3>4. Teste do Slider</h3>
            <p>Testa o slider de zoom diretamente.</p>
            <button class="test-button" onclick="testSliderDirect()">Testar Slider</button>
            <div id="slider-status" class="status" style="display: none;"></div>
        </div>

        <div class="debug-section">
            <h3>5. Console Output</h3>
            <p>Logs capturados durante os testes:</p>
            <div id="console-output" class="console-output">
                Nenhum log capturado ainda. Execute os testes acima.
            </div>
            <button class="test-button" onclick="clearConsole()">Limpar Console</button>
        </div>

        <div class="debug-section">
            <h3>6. Comandos Manuais</h3>
            <p>Execute estes comandos diretamente no console do navegador (F12):</p>
            <div class="console-output">
// Verificar se os elementos existem
document.getElementById('btn-zoom-in')
document.getElementById('btn-zoom-out')
document.getElementById('zoom-slider')

// Testar funções globais (se disponíveis)
window.testZoomButtons()
window.forceZoomIn()
window.forceZoomOut()
window.testSlider()

// Simular cliques manuais
document.getElementById('btn-zoom-in').click()
document.getElementById('btn-zoom-out').click()
            </div>
        </div>
    </div>

    <script>
        let logs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            updateConsoleOutput();
        }

        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            if (logs.length === 0) {
                output.textContent = 'Nenhum log capturado ainda. Execute os testes acima.';
            } else {
                output.textContent = logs.join('\n');
                output.scrollTop = output.scrollHeight;
            }
        }

        function clearConsole() {
            logs = [];
            updateConsoleOutput();
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
            addLog(`[${type.toUpperCase()}] ${message}`);
        }

        function getMainWindow() {
            // Tentar acessar a janela principal
            return window.opener || window.parent || window;
        }

        function checkElements() {
            try {
                const mainWindow = getMainWindow();
                const modal = mainWindow.document.getElementById('image-edit-modal');
                const zoomIn = mainWindow.document.getElementById('btn-zoom-in');
                const zoomOut = mainWindow.document.getElementById('btn-zoom-out');
                const slider = mainWindow.document.getElementById('zoom-slider');
                const zoomValue = mainWindow.document.getElementById('zoom-value');

                const results = {
                    modal: !!modal,
                    zoomIn: !!zoomIn,
                    zoomOut: !!zoomOut,
                    slider: !!slider,
                    zoomValue: !!zoomValue
                };

                addLog('Elementos encontrados: ' + JSON.stringify(results, null, 2));

                if (modal && zoomIn && zoomOut && slider) {
                    showStatus('elements-status', '✅ Todos os elementos principais encontrados!', 'success');
                    
                    // Verificar se o modal está visível
                    const modalStyle = mainWindow.getComputedStyle(modal);
                    addLog(`Modal display: ${modalStyle.display}, visibility: ${modalStyle.visibility}`);
                    
                    if (zoomIn) {
                        const btnStyle = mainWindow.getComputedStyle(zoomIn);
                        addLog(`Botão zoom in - display: ${btnStyle.display}, pointer-events: ${btnStyle.pointerEvents}`);
                    }
                } else {
                    showStatus('elements-status', '❌ Alguns elementos não encontrados', 'error');
                }
            } catch (error) {
                showStatus('elements-status', '❌ Erro ao verificar elementos: ' + error.message, 'error');
                addLog('Erro: ' + error.message);
            }
        }

        function testDirectClicks() {
            try {
                const mainWindow = getMainWindow();
                const zoomIn = mainWindow.document.getElementById('btn-zoom-in');
                const zoomOut = mainWindow.document.getElementById('btn-zoom-out');

                if (zoomIn && zoomOut) {
                    addLog('Simulando clique no botão zoom in...');
                    zoomIn.click();
                    
                    setTimeout(() => {
                        addLog('Simulando clique no botão zoom out...');
                        zoomOut.click();
                    }, 1000);
                    
                    showStatus('clicks-status', '✅ Cliques simulados! Verifique o console para logs.', 'success');
                } else {
                    showStatus('clicks-status', '❌ Botões não encontrados', 'error');
                }
            } catch (error) {
                showStatus('clicks-status', '❌ Erro ao simular cliques: ' + error.message, 'error');
                addLog('Erro: ' + error.message);
            }
        }

        function testZoomFunctions() {
            try {
                const mainWindow = getMainWindow();
                
                if (mainWindow.forceZoomIn && mainWindow.forceZoomOut) {
                    addLog('Testando função forceZoomIn...');
                    mainWindow.forceZoomIn();
                    
                    setTimeout(() => {
                        addLog('Testando função forceZoomOut...');
                        mainWindow.forceZoomOut();
                    }, 1000);
                    
                    showStatus('functions-status', '✅ Funções de zoom testadas!', 'success');
                } else {
                    showStatus('functions-status', '⚠️ Funções de teste não disponíveis. Modal pode não estar aberto.', 'warning');
                }
            } catch (error) {
                showStatus('functions-status', '❌ Erro ao testar funções: ' + error.message, 'error');
                addLog('Erro: ' + error.message);
            }
        }

        function testSliderDirect() {
            try {
                const mainWindow = getMainWindow();
                
                if (mainWindow.testSlider) {
                    addLog('Testando slider diretamente...');
                    mainWindow.testSlider();
                    showStatus('slider-status', '✅ Slider testado!', 'success');
                } else {
                    const slider = mainWindow.document.getElementById('zoom-slider');
                    if (slider) {
                        const currentValue = parseFloat(slider.value);
                        const newValue = currentValue === 1 ? 1.5 : 1;
                        slider.value = newValue;
                        
                        // Disparar evento input
                        const event = new Event('input', { bubbles: true });
                        slider.dispatchEvent(event);
                        
                        addLog(`Slider alterado: ${currentValue} -> ${newValue}`);
                        showStatus('slider-status', '✅ Slider testado manualmente!', 'success');
                    } else {
                        showStatus('slider-status', '❌ Slider não encontrado', 'error');
                    }
                }
            } catch (error) {
                showStatus('slider-status', '❌ Erro ao testar slider: ' + error.message, 'error');
                addLog('Erro: ' + error.message);
            }
        }

        // Interceptar console.log se possível
        try {
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                addLog('CONSOLE: ' + args.join(' '));
            };
        } catch (error) {
            addLog('Não foi possível interceptar console.log');
        }

        addLog('Página de debug carregada. Execute os testes acima.');
    </script>
</body>
</html>
