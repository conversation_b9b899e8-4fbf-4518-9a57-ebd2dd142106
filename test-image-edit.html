<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Edição de Imagem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Teste - Funcionalidades de Edição de Imagem</h1>
        
        <div class="test-section">
            <h3>1. Teste dos Botões de Zoom</h3>
            <p>Teste se os botões de zoom (+/-) estão funcionando no modal de edição.</p>
            <button class="test-button" onclick="testZoomButtons()">Testar Botões de Zoom</button>
            <div id="zoom-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Teste do Botão "Editar Imagem"</h3>
            <p>Teste se o botão "Editar Imagem" aparece quando há uma imagem carregada.</p>
            <button class="test-button" onclick="testEditButton()">Testar Botão Editar</button>
            <div id="edit-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Teste de Visibilidade Condicional</h3>
            <p>Teste se o botão só aparece para imagens personalizadas (não para logo.webp).</p>
            <button class="test-button" onclick="testConditionalVisibility()">Testar Visibilidade</button>
            <div id="visibility-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. Abrir Aplicação Principal</h3>
            <p>Abrir a aplicação principal para teste manual.</p>
            <button class="test-button" onclick="openMainApp()">Abrir Aplicação</button>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
            element.style.display = 'block';
        }

        function testZoomButtons() {
            // Verificar se os elementos existem na página principal
            const hasZoomIn = parent.document.getElementById('btn-zoom-in');
            const hasZoomOut = parent.document.getElementById('btn-zoom-out');
            const hasZoomSlider = parent.document.getElementById('zoom-slider');

            if (hasZoomIn && hasZoomOut && hasZoomSlider) {
                showResult('zoom-test-result', '✅ Elementos de zoom encontrados! Teste manual necessário.', 'success');
            } else {
                showResult('zoom-test-result', '❌ Elementos de zoom não encontrados na página.', 'error');
            }
        }

        function testEditButton() {
            // Verificar se o botão de editar existe
            const editButton = parent.document.getElementById('btn-edit-image');
            
            if (editButton) {
                const isVisible = editButton.style.display !== 'none';
                if (isVisible) {
                    showResult('edit-test-result', '✅ Botão "Editar Imagem" encontrado e visível!', 'success');
                } else {
                    showResult('edit-test-result', '⚠️ Botão encontrado mas oculto (normal se não há imagem).', 'warning');
                }
            } else {
                showResult('edit-test-result', '❌ Botão "Editar Imagem" não encontrado.', 'error');
            }
        }

        function testConditionalVisibility() {
            const editButton = parent.document.getElementById('btn-edit-image');
            const previewImage = parent.document.getElementById('preview-image');
            
            if (editButton && previewImage) {
                const isDefaultImage = previewImage.src.includes('logo.webp');
                const isButtonVisible = editButton.style.display !== 'none';
                
                if (isDefaultImage && !isButtonVisible) {
                    showResult('visibility-test-result', '✅ Correto: Botão oculto para imagem padrão.', 'success');
                } else if (!isDefaultImage && isButtonVisible) {
                    showResult('visibility-test-result', '✅ Correto: Botão visível para imagem personalizada.', 'success');
                } else {
                    showResult('visibility-test-result', '⚠️ Estado inconsistente. Verificar lógica.', 'warning');
                }
            } else {
                showResult('visibility-test-result', '❌ Elementos não encontrados.', 'error');
            }
        }

        function openMainApp() {
            window.open('http://localhost:8000', '_blank');
        }
    </script>
</body>
</html>
